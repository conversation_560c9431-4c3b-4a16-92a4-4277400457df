import csv
import io
import json
import logging
from typing import List
from datetime import datetime
from fastapi import APIRouter, HTTPException, status, Depends, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from models import (
    SingleUploadRequest, UploadResponse, QueryRequest, QueryResponse, 
    IPLookupResponse, IPRecord
)
from auth import require_admin, log_admin_action
from database import execute_query, execute_insert, execute_one, DatabaseTransaction

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/export/")
async def export_ip_records(current_user: dict = Depends(require_admin)):
    """
    Export all IP records as CSV file.
    
    Streams CSV data to avoid memory issues with large datasets.
    """
    try:
        # Log the export action
        await log_admin_action(
            admin_id=current_user["id"],
            action_type="export",
            details={"exported_by": current_user["username"]}
        )
        
        # Create CSV content generator
        async def generate_csv():
            # CSV header
            yield "ip_cidr,ip_version,country,owner,added_at,comment\n"
            
            # Query all records in chunks
            offset = 0
            chunk_size = 1000
            
            while True:
                query = """
                    SELECT ip_cidr, ip_version, country, owner, added_at, comment
                    FROM ip_records 
                    ORDER BY id
                    LIMIT :limit OFFSET :offset
                """
                
                records = await execute_query(query, {
                    "limit": chunk_size,
                    "offset": offset
                })
                
                if not records:
                    break
                
                # Convert records to CSV rows
                for record in records:
                    # Escape CSV fields properly
                    row = [
                        record["ip_cidr"],
                        str(record["ip_version"]),
                        record["country"].replace('"', '""'),
                        record["owner"].replace('"', '""'),
                        record["added_at"].isoformat() if record["added_at"] else "",
                        (record["comment"] or "").replace('"', '""')
                    ]
                    
                    # Format as CSV line
                    csv_line = ",".join(f'"{field}"' for field in row) + "\n"
                    yield csv_line
                
                offset += chunk_size
        
        # Generate filename with timestamp
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        filename = f"ip_records_{timestamp}.csv"
        
        logger.info(f"CSV export initiated by user: {current_user['username']}")
        
        return StreamingResponse(
            generate_csv(),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"Export error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error during export operation"
        )


@router.post("/upload/", response_model=UploadResponse)
async def upload_ip_records(
    current_user: dict = Depends(require_admin),
    file: UploadFile = File(None),
    ip: str = Form(None),
    country: str = Form(None),
    owner: str = Form(None),
    comment: str = Form(None)
):
    """
    Upload IP records either as single record or bulk file.
    
    Supports both single record JSON and bulk CSV file uploads.
    """
    try:
        if file:
            # Bulk file upload
            return await handle_bulk_upload(file, current_user)
        elif ip and country and owner:
            # Single record upload
            return await handle_single_upload(
                SingleUploadRequest(ip=ip, country=country, owner=owner, comment=comment),
                current_user
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either provide a file for bulk upload or ip/country/owner for single upload"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error during upload operation"
        )


async def handle_single_upload(request: SingleUploadRequest, current_user: dict) -> UploadResponse:
    """Handle single IP record upload."""
    try:
        # Create IP record from input
        ip_record = IPRecord.from_cidr(
            cidr_str=request.ip,
            country=request.country,
            owner=request.owner,
            comment=request.comment
        )
        
        # Check for overlapping ranges
        overlap_query = """
            SELECT COUNT(*) as count
            FROM ip_records 
            WHERE ip_start <= :ip_end 
            AND ip_end >= :ip_start
        """
        
        overlap_result = await execute_one(overlap_query, {
            "ip_start": ip_record.ip_start,
            "ip_end": ip_record.ip_end
        })
        
        if overlap_result and overlap_result["count"] > 0:
            return UploadResponse(
                inserted=0,
                skipped_conflicts=1,
                errors=[{"line": 1, "reason": f"Overlaps existing range"}]
            )
        
        # Check for exact duplicate
        duplicate_query = """
            SELECT COUNT(*) as count
            FROM ip_records 
            WHERE ip_cidr = :ip_cidr 
            AND country = :country 
            AND owner = :owner
        """
        
        duplicate_result = await execute_one(duplicate_query, {
            "ip_cidr": ip_record.ip_cidr,
            "country": ip_record.country,
            "owner": ip_record.owner
        })
        
        if duplicate_result and duplicate_result["count"] > 0:
            return UploadResponse(
                inserted=0,
                skipped_duplicates=1
            )
        
        # Insert the record
        insert_query = """
            INSERT INTO ip_records (ip_cidr, ip_version, ip_start, ip_end, country, owner, added_at, comment)
            VALUES (:ip_cidr, :ip_version, :ip_start, :ip_end, :country, :owner, :added_at, :comment)
        """
        
        await execute_insert(insert_query, {
            "ip_cidr": ip_record.ip_cidr,
            "ip_version": ip_record.ip_version,
            "ip_start": ip_record.ip_start,
            "ip_end": ip_record.ip_end,
            "country": ip_record.country,
            "owner": ip_record.owner,
            "added_at": ip_record.added_at,
            "comment": ip_record.comment
        })
        
        # Log the action
        await log_admin_action(
            admin_id=current_user["id"],
            action_type="single_upload",
            details={
                "ip_cidr": ip_record.ip_cidr,
                "country": ip_record.country,
                "owner": ip_record.owner
            }
        )
        
        logger.info(f"Single record uploaded by {current_user['username']}: {ip_record.ip_cidr}")
        
        return UploadResponse(inserted=1)
        
    except ValueError as e:
        return UploadResponse(
            inserted=0,
            skipped_invalid=1,
            errors=[{"line": 1, "reason": str(e)}]
        )


async def handle_bulk_upload(file: UploadFile, current_user: dict) -> UploadResponse:
    """Handle bulk file upload with transaction support."""
    # Validate file
    if file.size > 10 * 1024 * 1024:  # 10MB limit
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="File size exceeds 10MB limit"
        )
    
    if not file.filename.endswith(('.txt', '.csv')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be .txt or .csv format"
        )
    
    # Read and parse file
    content = await file.read()
    text_content = content.decode('utf-8')
    
    lines = text_content.strip().split('\n')
    total_lines = len(lines)
    
    if total_lines == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File is empty"
        )
    
    # Process records
    valid_records = []
    errors = []
    conflicts = 0
    duplicates = 0
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
            
        try:
            # Parse CSV line: ip_cidr,country,owner,comment
            parts = [part.strip().strip('"') for part in line.split(',')]
            if len(parts) < 3:
                errors.append({
                    "line": line_num,
                    "reason": "Insufficient columns (expected: ip_cidr,country,owner,comment)"
                })
                continue
            
            ip_cidr = parts[0]
            country = parts[1]
            owner = parts[2]
            comment = parts[3] if len(parts) > 3 else None
            
            # Create IP record
            ip_record = IPRecord.from_cidr(ip_cidr, country, owner, comment)
            valid_records.append((ip_record, line_num))
            
        except Exception as e:
            errors.append({"line": line_num, "reason": str(e)})
    
    # Check error threshold (5%)
    invalid_count = len(errors)
    error_rate = invalid_count / total_lines if total_lines > 0 else 0
    
    if error_rate > 0.05:  # More than 5% errors
        await log_admin_action(
            admin_id=current_user["id"],
            action_type="bulk_upload_failed",
            details={
                "filename": file.filename,
                "total_lines": total_lines,
                "invalid_count": invalid_count,
                "error_rate": error_rate
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "More than 5% of lines failed validation. Bulk upload aborted.",
                "errors": errors[:50]  # Limit error list
            }
        )
    
    # Insert valid records in transaction
    inserted = 0
    async with DatabaseTransaction():
        for ip_record, line_num in valid_records:
            try:
                # Check for overlaps and duplicates
                overlap_query = """
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_start <= :ip_end 
                    AND ip_end >= :ip_start
                """
                
                overlap_result = await execute_one(overlap_query, {
                    "ip_start": ip_record.ip_start,
                    "ip_end": ip_record.ip_end
                })
                
                if overlap_result and overlap_result["count"] > 0:
                    conflicts += 1
                    errors.append({
                        "line": line_num,
                        "reason": f"Overlaps existing range"
                    })
                    continue
                
                # Check for exact duplicate
                duplicate_query = """
                    SELECT COUNT(*) as count
                    FROM ip_records 
                    WHERE ip_cidr = :ip_cidr 
                    AND country = :country 
                    AND owner = :owner
                """
                
                duplicate_result = await execute_one(duplicate_query, {
                    "ip_cidr": ip_record.ip_cidr,
                    "country": ip_record.country,
                    "owner": ip_record.owner
                })
                
                if duplicate_result and duplicate_result["count"] > 0:
                    duplicates += 1
                    continue
                
                # Insert record
                insert_query = """
                    INSERT INTO ip_records (ip_cidr, ip_version, ip_start, ip_end, country, owner, added_at, comment)
                    VALUES (:ip_cidr, :ip_version, :ip_start, :ip_end, :country, :owner, :added_at, :comment)
                """
                
                await execute_insert(insert_query, {
                    "ip_cidr": ip_record.ip_cidr,
                    "ip_version": ip_record.ip_version,
                    "ip_start": ip_record.ip_start,
                    "ip_end": ip_record.ip_end,
                    "country": ip_record.country,
                    "owner": ip_record.owner,
                    "added_at": ip_record.added_at,
                    "comment": ip_record.comment
                })
                
                inserted += 1
                
            except Exception as e:
                errors.append({
                    "line": line_num,
                    "reason": f"Database error: {str(e)}"
                })
    
    # Log the action
    await log_admin_action(
        admin_id=current_user["id"],
        action_type="bulk_upload",
        details={
            "filename": file.filename,
            "total_lines": total_lines,
            "inserted": inserted,
            "conflicts": conflicts,
            "duplicates": duplicates,
            "invalid": invalid_count
        }
    )
    
    logger.info(f"Bulk upload by {current_user['username']}: {inserted} inserted, {conflicts} conflicts, {duplicates} duplicates")
    
    return UploadResponse(
        inserted=inserted,
        skipped_conflicts=conflicts,
        skipped_invalid=invalid_count,
        skipped_duplicates=duplicates,
        errors=errors
    )


@router.post("/query/", response_model=QueryResponse)
async def query_ip_records(
    request: QueryRequest,
    current_user: dict = Depends(require_admin)
):
    """
    Query IP records with filtering, pagination, and sorting.

    Supports dynamic filtering by various columns with different operators.
    """
    try:
        # Validate filters
        allowed_columns = {"ip_cidr", "ip_version", "country", "owner", "added_at"}
        allowed_operators = {"equals", "contains", "gte", "lte"}

        where_conditions = []
        query_params = {}
        param_counter = 0

        for filter_item in request.filters:
            if filter_item.column not in allowed_columns:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid column: {filter_item.column}"
                )

            if filter_item.operator not in allowed_operators:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid operator: {filter_item.operator}"
                )

            param_name = f"param_{param_counter}"
            param_counter += 1

            if filter_item.operator == "equals":
                where_conditions.append(f"LOWER({filter_item.column}) = LOWER(:{param_name})")
                query_params[param_name] = filter_item.value
            elif filter_item.operator == "contains":
                where_conditions.append(f"LOWER({filter_item.column}) LIKE LOWER(:{param_name})")
                query_params[param_name] = f"%{filter_item.value}%"
            elif filter_item.operator == "gte":
                where_conditions.append(f"{filter_item.column} >= :{param_name}")
                query_params[param_name] = filter_item.value
            elif filter_item.operator == "lte":
                where_conditions.append(f"{filter_item.column} <= :{param_name}")
                query_params[param_name] = filter_item.value

        # Build WHERE clause
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # Validate sort column
        sort_column = request.sort.get("column", "added_at")
        sort_order = request.sort.get("order", "desc").upper()

        if sort_column not in allowed_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid sort column: {sort_column}"
            )

        if sort_order not in ["ASC", "DESC"]:
            sort_order = "DESC"

        # Get total count
        count_query = f"""
            SELECT COUNT(*) as total
            FROM ip_records
            {where_clause}
        """

        count_result = await execute_one(count_query, query_params)
        total_matches = count_result["total"] if count_result else 0

        # Get paginated results
        skip = request.pagination.get("skip", 0)
        limit = min(request.pagination.get("limit", 100), 1000)  # Max 1000 per page

        data_query = f"""
            SELECT ip_cidr, ip_version, country, owner, added_at, comment
            FROM ip_records
            {where_clause}
            ORDER BY {sort_column} {sort_order}
            LIMIT :limit OFFSET :skip
        """

        query_params.update({"skip": skip, "limit": limit})

        records = await execute_query(data_query, query_params)

        # Convert to response models
        response_data = []
        for record in records:
            response_data.append(IPLookupResponse(
                ip_cidr=record["ip_cidr"],
                ip_version=record["ip_version"],
                country=record["country"],
                owner=record["owner"],
                added_at=record["added_at"],
                comment=record["comment"]
            ))

        # Log the query action
        await log_admin_action(
            admin_id=current_user["id"],
            action_type="query",
            details={
                "filters": [f.model_dump() for f in request.filters],
                "total_matches": total_matches,
                "returned": len(response_data)
            }
        )

        logger.info(f"Query by {current_user['username']}: {total_matches} matches, {len(response_data)} returned")

        return QueryResponse(
            total_matches=total_matches,
            returned=len(response_data),
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Query error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error during query operation"
        )
