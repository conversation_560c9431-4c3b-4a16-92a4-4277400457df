import os
import sqlite3
from contextlib import asynccontextmanager
from databases import Database
from sqlmodel import SQLModel, create_engine
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
import logging

logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./ip_lookup.db")
ASYNC_DATABASE_URL = DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://")

# Create engines
engine = create_engine(DATABASE_URL, echo=False)
async_engine = create_async_engine(ASYNC_DATABASE_URL, echo=False)

# Database instance for async operations
database = Database(ASYNC_DATABASE_URL)


async def create_db_and_tables():
    """Create database tables if they don't exist."""
    try:
        # Create tables using sync engine for initial setup
        SQLModel.metadata.create_all(engine)
        
        # Create indexes for better performance
        with sqlite3.connect("./ip_lookup.db") as conn:
            cursor = conn.cursor()
            
            # Create composite index for IP range lookups
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_ip_range 
                ON ip_records(ip_start, ip_end)
            """)
            
            # Create additional indexes for filtering
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_country 
                ON ip_records(country)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_owner 
                ON ip_records(owner)
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_added_at 
                ON ip_records(added_at)
            """)
            
            # Index for login attempts
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_login_attempts_username_time 
                ON login_attempts(username, timestamp)
            """)
            
            # Index for audit logs
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_audit_admin_time 
                ON audit_logs(admin_id, timestamp)
            """)
            
            conn.commit()
            
        logger.info("Database tables and indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


async def get_database():
    """Get database connection for dependency injection."""
    return database


@asynccontextmanager
async def get_db_connection():
    """Context manager for database connections."""
    try:
        await database.connect()
        yield database
    finally:
        await database.disconnect()


async def check_database_health():
    """Check if database is accessible and healthy."""
    try:
        await database.fetch_one("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


# Database startup and shutdown handlers
async def startup_database():
    """Connect to database on startup."""
    try:
        await database.connect()
        logger.info("Database connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise


async def shutdown_database():
    """Disconnect from database on shutdown."""
    try:
        await database.disconnect()
        logger.info("Database disconnected successfully")
    except Exception as e:
        logger.error(f"Error disconnecting from database: {e}")


# Utility functions for database operations
async def execute_query(query: str, values: dict = None):
    """Execute a query and return results."""
    try:
        if values:
            return await database.fetch_all(query, values)
        else:
            return await database.fetch_all(query)
    except Exception as e:
        logger.error(f"Query execution failed: {query}, Error: {e}")
        raise


async def execute_one(query: str, values: dict = None):
    """Execute a query and return single result."""
    try:
        if values:
            return await database.fetch_one(query, values)
        else:
            return await database.fetch_one(query)
    except Exception as e:
        logger.error(f"Query execution failed: {query}, Error: {e}")
        raise


async def execute_insert(query: str, values: dict):
    """Execute an insert query and return the last row id."""
    try:
        return await database.execute(query, values)
    except Exception as e:
        logger.error(f"Insert execution failed: {query}, Error: {e}")
        raise


# Transaction support
class DatabaseTransaction:
    """Context manager for database transactions."""
    
    def __init__(self):
        self.transaction = None
    
    async def __aenter__(self):
        self.transaction = await database.transaction()
        return self.transaction
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.transaction.rollback()
        else:
            await self.transaction.commit()


# Initialize database on module import
async def init_database():
    """Initialize database with tables and indexes."""
    await create_db_and_tables()
    await startup_database()
