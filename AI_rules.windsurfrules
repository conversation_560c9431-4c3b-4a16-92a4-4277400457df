Overview and Objectives
As a senior full-stack developer, your goal is to build a minimalist, dark‐themed IP lookup web application, analogous to IPWhois.io or AbuseIPDB, using modern Python on the backend and a lightweight database to store IP metadata. The system will consist of three pages:

1. Landing Page: Allows any visitor to input an IPv4, IPv6, or CIDR (IP subnet) and view lookup results from the local database.

2. Login Page: Authenticates administrators who wish to manage the IP database.

3. Registration account page

4. Admin Dashboard: Grants administrators three main capabilities:

	Download existing IP entries (CSV/text)

	Upload new entries in bulk (text/CSV) or one‐by‐one

	Filter/search the database by any metadata field (e.g., Country)




Below is a comprehensive set of rules, specifications, and “AI‐style” business logic that will guide the implementation from front‐end to back‐end, database schema design, security considerations, and validation requirements. Follow these rules closely to deliver a robust, maintainable, and secure service.

** Execute the command for me, if error pops out troubleshoot and fix it

## 1. Technology Stack

- **Backend Framework**:  
  - **FastAPI** (Python 3.10+).  
    - Rationale: High performance, asynchronous support, built‐in data validation via Pydantic, automatic OpenAPI docs.
- **Database**:  
  - **SQLite** (initial proof‐of‐concept / small deployments)  
    - Rationale: Zero‐configuration, file‐based, lightweight, works well for bulk lookups.  
  - (Optionally swappable to PostgreSQL or MySQL by changing only the connection string and minimal SQL dialect adjustments.)
- **ORM**:  
  - **SQLModel** (by Sebastián Ramírez) or **SQLAlchemy + Pydantic models**.  
    - Rationale: Native integration with FastAPI’s type hints and data validation.
- **Frontend Framework**:  
  - **Vanilla HTML/CSS/JavaScript** (no heavy SPA frameworks).  
  - **Tailwind CSS** for rapid theming (dark mode by default).  
- **Authentication**:  
  - **OAuth2 Password flow** (JWT tokens) via FastAPI’s utilities.  
  - **Passlib** (bcrypt) for password hashing.
- **Packaging & Deployment**:  
  - **Docker** (single‐container for API + lightweight SQLite file).  
- **Testing**:  
  - **Pytest** for unit tests (API endpoints, data validation).  
  - **HTTPX** for async integration tests against FastAPI.  

---

## 2. High-Level Architecture

```
 ┌─────────────────────────────────────────────────────────┐
 │                         CLIENT                         │
 │                                                         │
 │  ┌────────────┐      ┌──────────────┐       ┌──────────┐ │
 │  │ Landing    │◀────▶│   FastAPI    │◀─────▶│ SQLite   │ │
 │  │ Page (JS)  │ HTTP │  Backend     │ SQL   │ Database │ │
 │  └────────────┘      └──────────────┘       └──────────┘ │
 │                                                         │
 │          ↑                                         ↑    │
 │          │  /login  → Login Page (JS, forms)      │    │
 │          ↓                                           │  │
 │  ┌──────────────────┐                                │  │
 │  │ Admin Dashboard  │◀────(Bearer token)───(JWT)──▶ │  │
 │  │ (JS/HTML/Tailwind) │                             │  │
 │  └──────────────────┘                                │  │
 └─────────────────────────────────────────────────────────┘
```

- **Client (Browser)**  
  - Static HTML/CSS/JS files served by FastAPI’s `StaticFiles` or separately via CDN.  
  - All pages share common Tailwind CSS config (dark theme variables).  
  - Vanilla JS or minimal Alpine.js for DOM interactivity (e.g., show/hide search results, login form validation).
- **API Endpoints**  
  - **Public** (no authentication):  
    - `GET  /health-check/` → 200 OK (API is up).  
    - `POST /api/v1/lookup/` → Accepts payload `{ "query": "IP or CIDR" }`, returns metadata array or single record.  
  - **Auth**:  
    - `POST /api/v1/auth/login/` → Accepts `{ "username": "...", "password": "..." }`, returns `{ "access_token": "...", "token_type": "bearer" }`.  
    - `POST /api/v1/auth/refresh/` → Accepts valid refresh token (optional).  
  - **Protected (Admin)** (Bearer JWT required):  
    - `GET    /api/v1/admin/export/` → Streams a CSV file of all IP records.  
    - `POST   /api/v1/admin/upload/` → Accepts `multipart/form-data` with either:  
      - `file: text/csv` (bulk upload, comma‐delimited lines)  
      - OR a JSON payload `{ "ip": "...", "country": "...", "owner": "...", ... }` for single.  
    - `POST   /api/v1/admin/query/` → Accepts filters (e.g. `{ "column": "Country", "value": "India" }` plus pagination options) and returns matching records.  

---

## 3. Database Schema & Data Model Rules

All IP records must conform to a strict schema to allow efficient lookups, range queries, and filtering.

### 3.1. Table: `ip_records`

| Column Name       | Type                                     | Constraints & Description                                                 |
|-------------------|------------------------------------------|----------------------------------------------------------------------------|
| `id`              | `INTEGER` (AUTO INCREMENT, PK)           | Primary key.                                                               |
| `ip_start`        | `INTEGER`                                | Unsigned 128‐bit integer representation of the first IP in the range.      |
| `ip_end`          | `INTEGER`                                | Unsigned 128‐bit integer representation of the last IP in the range.       |
| `ip_cidr`         | `TEXT`                                   | Canonical CIDR notation (e.g., `*********/24`, `2001:db8::/64`).            |
| `ip_version`      | `SMALLINT`                               | `4` if IPv4, `6` if IPv6.                                                   |
| `country`         | `TEXT`                                   | Two‐letter ISO country code or full country name.                           |
| `owner`           | `TEXT`                                   | Owner/organization name (e.g., “Cloudflare Inc.”).                           |
| `added_at`        | `TIMESTAMP`                              | UTC timestamp when this record was inserted (default: current timestamp).    |
| `comment`         | `TEXT` (nullable)                        | Optional free‐text comment or notes.                                        |

> **Rules / Validation for Insertion**  
> 1. **IP Parsing & Conversion**  
>    - On upload (bulk or single), parse the input field (string) for:  
>      - **IPv4**: Accept dotted form (e.g., `***********`) or CIDR (e.g., `***********/28`).  
>      - **IPv6**: Accept canonical or compressed (e.g., `2001:db8::1` or `2001:db8:0:0:0:0:0:1/128`).  
>      - **IP‐Subnet**: If user enters `***********/16`, compute `ip_start = integer(***********)` and `ip_end = integer(***************)`.  
>    - Validation failures (invalid IP format, invalid mask) must return HTTP 400 with a clear JSON error:  
>      ```json
>      { "detail": "Invalid IPv4/CIDR provided: '203.0.302.1'" }
>      ```  
> 2. **Consistent Numeric Storage**  
>    - Always convert parsed addresses to integer for `ip_start`/`ip_end` using Python’s `ipaddress` standard library.  
>    - Example:  
>      ```python
>      import ipaddress
>      net = ipaddress.ip_network("***********/24")
>      ip_start = int(net.network_address)   # 3232235520
>      ip_end   = int(net.broadcast_address) # 3232235775
>      ```  
>    - For single IP (no `/`), treat as /32 or /128 range.  
> 3. **Indexing**  
>    - Create a composite **BRIN** (or B‐TREE for SQLite) index on `(ip_start, ip_end)` to allow rapid range lookups given a target IP.  
>    - Index on `country`, `owner` for filtering.  

---

## 4. API & Validation Rules

### 4.1. Public Lookup Endpoint

- **Endpoint**: `POST /api/v1/lookup/`  
- **Payload Schema**:  
  ```json5
  {
    "query": "string"  // required; one of: IPv4, IPv6, or CIDR
  }
  ```
- **Validation Logic**:  
  1. Use `ipaddress` to parse `query`.  
     - If it’s a single IP (v4 or v6), convert it to its integer form:  
       ```python
       target = int(ipaddress.ip_address(query))
       ```  
     - If it’s a CIDR, treat it as a network:  
       ```python
       network = ipaddress.ip_network(query, strict=False)
       target_start = int(network.network_address)
       target_end   = int(network.broadcast_address)
       ```  
  2. **Lookup Strategy**:  
     - **Single IP**: Perform `SELECT * FROM ip_records WHERE ip_start <= target AND ip_end >= target LIMIT 1`.  
     - **CIDR**: Return all database entries that overlap the user’s requested network range:  
       ```sql
       SELECT * 
         FROM ip_records 
        WHERE ip_start <= :cidr_end 
          AND ip_end   >= :cidr_start
       ORDER BY ip_start ASC;
       ```  
  3. **Response**:  
     - **200 OK**: Return an array of matched records. Each record must be serialized as:  
       ```json5
       {
         "ip_cidr": "*********/24",
         "ip_version": 4,
         "country": "United States",
         "owner": "Example Corp",
         "added_at": "2025-05-30T14:22:10Z",
         "comment": "Reserved for testing"
       }
       ```  
     - **404 Not Found**: If no matching entry, return:  
       ```json
       { "detail": "No data found for *************" }
       ```  
     - **400 Bad Request**: If `query` is not a valid IP or CIDR:  
       ```json
       { "detail": "Invalid IP/CIDR format: '203.0.113.999/32'" }
       ```  

### 4.2. Authentication Endpoints

- **Login**  
  - **Endpoint**: `POST /api/v1/auth/login/`  
  - **Payload**:  
    ```json5
    {
      "username": "string",  // required
      "password": "string"   // required
    }
    ```  
  - **Logic**:  
    1. Fetch user by username (table: `admin_users`).  
    2. Compare hashed password using `bcrypt` (Passlib).  
    3. If valid, issue:  
       - **Access Token**: JWT (expires in 15 minutes)  
       - **Refresh Token** (optional; expires in 7 days)  
    4. If invalid, return `401 Unauthorized`:  
       ```json
       { "detail": "Invalid username or password." }
       ```  
- **Refresh** (optional)  
  - **Endpoint**: `POST /api/v1/auth/refresh/`  
  - **Payload**:  
    ```json
    { "refresh_token": "string" }
    ```  
  - **Logic**: Validate refresh token; if valid, return new access token. Otherwise `401 Unauthorized`.  

### 4.3. Admin Endpoints (All Require `Authorization: Bearer <access_token>`)  

- **Export All IPs**  
  - **Endpoint**: `GET /api/v1/admin/export/`  
  - **Response**:  
    - **200 OK** with `Content-Type: text/csv`, `Content-Disposition: attachment; filename="ip_records_<timestamp>.csv"`.  
    - CSV columns:  
      ```
      ip_cidr,ip_version,country,owner,added_at,comment
      *********/24,4,United States,Example Corp,2025-05-30T14:22:10Z,Reserved for testing
      ...  
      ```  
  - **Rules**:  
    - Stream results in chunks (e.g., 1,000 rows per chunk) to avoid loading entire table in memory.  
- **Upload IP Entries**  
  - **Endpoint**: `POST /api/v1/admin/upload/`  
  - **Payload Options**:  
    1. **Single JSON** (Content‐Type: application/json):  
       ```json5
       {
         "ip": "***********/24",
         "country": "India",
         "owner": "Bharti Ltd.",
         "comment": "New region allocation"
       }
       ```  
    2. **Bulk File** (Content‐Type: multipart/form-data):  
       - Field `file`: text file (UTF‐8), each line = One record, comma‐delimited.  
       - **Order of columns** (strict): `ip_cidr,country,owner,comment`  
       - Example lines:  
         ```
         ************/24,Australia,Telstra,Corporate block  
         2001:db8::/32,Canada,Bell Canada,  
         ```  
     - **Rules for Both**:  
       1. **Validate** each line/JSON:  
          - `ip` must parse successfully via `ipaddress.ip_network(..., strict=False)`.  
          - `country` must be non‐empty; optionally validate against a whitelist of ISO country names/codes.  
          - `owner`: non‐empty string of max length 255 chars.  
          - `comment`: optional, max length 500 chars.  
       2. **Detect Duplicate Ranges**:  
          - For each inserted CIDR range, query the DB for existing overlapping ranges:  
            ```sql
            SELECT COUNT(*) 
              FROM ip_records 
             WHERE ip_start <= :new_end 
               AND ip_end   >= :new_start;
            ```  
          - If any overlap is found, treat as conflict: skip/record as warning in response.  
       3. **Transactions**:  
          - For bulk upload, wrap entire ingestion in a single DB transaction.  
          - If **more than 5%** of lines are invalid or conflicting, **roll back** the entire batch and return `400` with details of errors.  
          - Otherwise, commit only valid lines; return a JSON summary:  
            ```json5
            {
              "inserted": 950,
              "skipped_conflicts": 30,
              "skipped_invalid": 20,
              "errors": [
                { "line": 5, "reason": "Invalid CIDR format" },
                { "line": 12, "reason": "Overlaps existing range ************/24" }
                // ...
              ]
            }
            ```  
       4. **Idempotency**:  
          - If the exact same IP range + metadata already exists (by comparing `ip_cidr`, `country`, `owner`), skip without error; count as “skipped_duplicate.”  
- **Filtered Search / Query**  
  - **Endpoint**: `POST /api/v1/admin/query/`  
  - **Payload** (JSON):  
    ```json5
    {
      "filters": [
        { "column": "country", "operator": "equals",  "value": "India" },
        { "column": "owner",   "operator": "contains", "value": "Telstra" }
      ],
      "pagination": { "skip": 0, "limit": 100 },
      "sort":        { "column": "added_at", "order": "desc" }
    }
    ```  
  - **Allowed Columns**: `ip_cidr`, `ip_version`, `country`, `owner`, `added_at`.  
  - **Operators**:  
    - `"equals"` → exact string (case-insensitive for text columns).  
    - `"contains"` → SQL `LIKE '%value%'` (case‐insensitive).  
    - `"gte"`, `"lte"` → only valid for `added_at` or `ip_version`.  
  - **Rules**:  
    1. **Sanitize** inputs—reject any unknown column or operator (`400 Bad Request`).  
    2. Build a dynamic SQLAlchemy (or SQLModel) query with combined `AND` conditions.  
    3. Apply `ORDER BY` then `OFFSET skip LIMIT limit`.  
    4. Return result as:  
       ```json5
       {
         "total_matches": 1234,
         "returned": 100,
         "data": [
           {
             "ip_cidr": "************/24",
             "ip_version": 4,
             "country": "Australia",
             "owner": "Telstra",
             "added_at": "2025-05-20T08:15:00Z",
             "comment": "Corporate block"
           },
           // ... (up to 100)
         ]
       }
       ```  
    5. If no matches, return `200 OK` with `"total_matches": 0` and `"data": []`.  

---

## 5. Admin User Management

- **Table: `admin_users`**  

  | Column Name     | Type                             | Constraints/Description                                                        |
  |-----------------|----------------------------------|---------------------------------------------------------------------------------|
  | `id`            | `INTEGER` (PK, auto‐inc)         | Unique ID                                                                        |
  | `username`      | `TEXT`                           | Unique, min 4, max 50 chars, lowercase letters, digits, underscores only         |
  | `hashed_pwd`    | `TEXT`                           | Bcrypt‐hashed password (salt + cost embedded)                                     |
  | `is_active`     | `BOOLEAN`                        | True if user can log in                                                          |
  | `created_at`    | `TIMESTAMP`                      | UTC timestamp when account was created                                            |
  | `last_login_at` | `TIMESTAMP` (nullable)           | Updated on each successful authentication                                         |

- **Rules**  
  1. **Password Policy**: At least 8 characters, including uppercase, lowercase, digit, and symbol.  
  2. **Account Creation/Deletion**: Only possible via direct DB migration or a separate “super‐admin” CLI. (No self‐service.)  
  3. **Lockout**: After 5 failed login attempts within 10 minutes, lock out for 30 minutes. Track attempts in a separate lightweight table `login_attempts`.  

---

## 6. UI/UX & Theming (Minimalist Dark Theme)

### 6.1. Color Palette & Typography  
- **Background**: near‐black (`#121212`)  
- **Foreground Text**: off-white (`#E0E0E0`)  
- **Primary Accent**: teal (`#1ABC9C`) or cyan (`#2ECC71`) for buttons/links  
- **Secondary Accent**: light gray (`#282828`) for form fields, card backgrounds  
- **Font**: `Inter, sans‐serif`  
- **Heading Sizing**:  
  - `h1`: 2rem  
  - `h2`: 1.5rem  
  - `h3`: 1.25rem  
  - Body: 1rem  

### 6.2. Layout Guidelines

1. **Landing Page (Search)**  
   - **Header**  
     - Left: Logo (simple text “IP-Check”).  
     - Right: “Admin Login” link (routes to `/login/`).  
   - **Hero Section** (centered vertically & horizontally)  
     - Large prompt: “Enter IPv4, IPv6, or Subnet” (text‐input with placeholder).  
     - Rounded, shadowed input field (max-width: 400px).  
     - Under input, a “Lookup” button (full width up to 200px).  
     - Below: small text: “Powered by local IP database.”  
   - **Search Behavior**  
     - On submit, call `fetch('/api/v1/lookup', { method:'POST', body: JSON.stringify({ query }) })` asynchronously.  
     - **Loading Spinner**: minimalist spinner (CSS‐only) in place of results.  
     - **Results Card**: if found, show a dark card with border‐accent color, listing:  
       ```
       • CIDR: ***********/24  
       • Version: 4  
       • Country: United States  
       • Owner: Example Org  
       • Added: May 30, 2025  
       • Comment: Corporate block  
       ```  
     - If no result, show “No data found for [input].”  
   - **Footer**  
     - Small links: “Docs” (if any), “Privacy Policy,” “Contact Email.”

2. **Login Page**  
   - Simple centered form on dark background.  
   - Fields:  
     - Username (input)  
     - Password (input type=password)  
     - “Login” button  
   - Show validation errors (e.g., “Invalid credentials”) in red below the form.  
   - Upon successful login, store JWT in `localStorage` or a secure `httpOnly` cookie, then redirect to `/admin/`.

3. **Admin Dashboard**  
   - **Layout**:  
     - Sidebar (vertical, left, width ~240px, background: `#1E1E1E`), with navigation links:  
       - “Dashboard Home” (could show summary metrics, e.g. total IP entries, last upload date)  
       - “Export IPs”  
       - “Upload IPs”  
       - “Search/Filter”  
       - “Logout”  
     - Main Content Area (right, remaining width, background: `#121212`).
   - **Dashboard Home**  
     - Show panels/cards:  
       - Total IP records (e.g., “1,234,567 record(s)”).  
       - Last bulk upload date (if any).  
       - Active admin users (count).  

   - **Export IPs Section**  
     - A single “Download CSV” button (when clicked, calls `GET /api/v1/admin/export/` and triggers file download).  
     - Show a brief note: “CSV includes all IP ranges and metadata.”

   - **Upload IPs Section**  
     - **Single Record Form**:  
       - Input fields:  
         1. IP or CIDR (text)  
         2. Country (text or dropdown of common countries)  
         3. Owner (text)  
         4. Comment (textarea, optional)  
       - “Upload Single” button.  
       - On success/failure, show inline alert (green for success, red for errors).  
     - **Bulk Upload Form**:  
       - File input (accept `.txt`, `.csv`).  
       - “Upload Bulk” button.  
       - After upload, show a summary table: inserted/skipped/etc.  
       - If >5% invalid, show “Bulk upload failed. See errors below” and list each line number + error.

   - **Search / Filter Section**  
     - Form at top:  
       - Dropdown “Column” (options: Country, Owner, CIDR, Added Date).  
       - Dropdown “Operator” (equals, contains, ≥, ≤ depending on column choice).  
       - Text/Date input “Value.”  
       - “Add Filter” button (builds array of filters).  
       - Each active filter displays as a removable pill (e.g., “Country = India ×”).  
       - Pagination controls: “Page X of Y,” “Prev,” “Next,” and “Results per page” (10, 25, 50, 100).  
     - **Results Table**:  
       - Columns: CIDR, Version, Country, Owner, Added At.  
       - Sorted by default by `added_at desc`.  
       - If no results: show “No matching records.”  

---

## 7. Security & Best Practices


2. **JWT Secret Management**  
   - Keep `SECRET_KEY` in environment variables; rotate every six months.  
   - Use secure algorithms (HS256 or RS256 if you want asymmetric).  
3. **Password Hashing**  
   - Use `bcrypt` via Passlib with a work factor ≥ 12.  
4. **CORS**  
   - If front‐end and API are served from the same origin, restrict CORS to that origin.  
   - In FastAPI, configure `CORSMiddleware(allow_origins=["https://your‐domain.com"], allow_methods=["GET","POST"], allow_headers=["*"])`.  
5. **Rate Limiting**  
   - For public lookup endpoint, enforce max **30 requests/minute per IP**.  
   - Use a lightweight in‐memory cache (e.g., Redis or even `aioredis`) to track client IP + request counts.  
6. **Input Sanitization**  
   - All user inputs must be validated via Pydantic models.  
   - Never incorporate user‐provided strings directly into raw SQL. Use parameterized queries (ORM automatically does this).  
7. **Database File Permissions**  
   - If using SQLite, ensure file has strict permissions (e.g., `chmod 600`).  
   - If switched to PostgreSQL/MySQL, use strong DB user credentials and restrict remote access to known application host.  
8. **File Upload Hardening**  
   - When accepting bulk uploads, check MIME type and file extension.  
   - Limit maximum file size to e.g. 10 MB (configurable).  
   - Stream file reads rather than reading entire file into memory.  

---

---

## 8. Data Retention & Privacy Policy

- **IP Data**  
  - Since IP ranges & metadata are non‐PII, retention is indefinite.  
  - If an admin requests deletion of certain ranges, implement a `DELETE FROM ip_records WHERE ip_cidr = :cidr`.  
- **Admin Credentials**  
  - Never store plaintext passwords.  
  - Archive old credentials when rotating users.  
- **Audit Logging**  
  - Maintain a table `audit_logs` (columns: `id`, `timestamp`, `admin_id`, `action_type`, `detail_json`).  
  - Actions to log:  
    1. Bulk upload (store filename, number of inserted, skipped).  
    2. Single record upload.  
    3. IP export.  
    4. Query with filters (record full filter JSON).  
  - Provide an admin function (not publicly exposed) to view recent audit entries.  

---

## 10. Sample Database Schema (SQLite / SQLModel)

```python
from datetime import datetime
from sqlmodel import Field, SQLModel
import ipaddress

class IPrecord(SQLModel, table=True):
    id:         int       = Field(default=None, primary_key=True)
    ip_cidr:    str       = Field(index=True, nullable=False)
    ip_version: int       = Field(nullable=False)
    ip_start:   int       = Field(nullable=False, index=True)
    ip_end:     int       = Field(nullable=False, index=True)
    country:    str       = Field(nullable=False, index=True)
    owner:      str       = Field(nullable=False, index=True)
    added_at:   datetime  = Field(default_factory=datetime.utcnow, nullable=False, index=True)
    comment:    str | None = Field(default=None)

    @classmethod
    def from_cidr(cls, cidr_str: str, country: str, owner: str, comment: str | None = None):
        network = ipaddress.ip_network(cidr_str, strict=False)
        return cls(
            ip_cidr=cidr_str,
            ip_version=network.version,
            ip_start=int(network.network_address),
            ip_end=int(network.broadcast_address),
            country=country.strip(),
            owner=owner.strip(),
            comment=comment.strip() if comment else None,
        )

class AdminUser(SQLModel, table=True):
    id:            int       = Field(default=None, primary_key=True)
    username:      str       = Field(nullable=False, unique=True, max_length=50)
    hashed_pwd:    str       = Field(nullable=False)
    is_active:     bool      = Field(default=True)
    created_at:    datetime  = Field(default_factory=datetime.utcnow)
    last_login_at: datetime | None = None

class AuditLog(SQLModel, table=True):
    id:          int       = Field(default=None, primary_key=True)
    timestamp:   datetime  = Field(default_factory=datetime.utcnow, nullable=False, index=True)
    admin_id:    int       = Field(nullable=False, index=True)
    action_type: str      = Field(nullable=False, max_length=50)  # e.g. "bulk_upload", "single_upload", "export", "query"
    detail_json: str      = Field(nullable=False)  # JSON string with details
```

> **Indexing Notes**  
> - `index=True` on `ip_start` and `ip_end` allows SQLite to use binary search for range overlap queries.  
> - Index on `country` and `owner` speeds up filtering in the admin search.  

---

## 11. Front-End Implementation Rules

1. **No JS Framework Overkill**  
   - Use **Vanilla JS** + **Tailwind**.  
   - If minimal reactivity is needed (e.g., dynamic filter pills), optionally pull in **Alpine.js** (≤ 10 KB gzipped).
2. **Single Codebase for HTML Templates**  
   - Use Jinja2 (FastAPI’s `Templates`) or serve static HTML files.  
   - Keep CSS in one file (Tailwind’s compiled CSS).  
   - Place all JS in a single `main.js` (modular sections for each page).
3. **Responsive Design**  
   - Use Tailwind’s responsive classes (`sm:`, `md:`, `lg:`) so everything works on mobile (hamburger menu for sidebar on small screens).
4. **Form Validation**  
   - **Client-Side**:  
     - For the landing page: only validate that input is non‐empty.  
     - For admin forms: check that required fields are non‐empty before submission.  
   - **Server-Side**: Pydantic will enforce stricter validation (e.g., valid IP format, string lengths).
5. **Error Handling / Alerts**  
   - Use “toast” notifications (small fixed position at top‐right) with dark backgrounds and contrasting text.  
   - On API error (HTTP 4xx/5xx), show toast with error detail.  
   - On success, show toast with confirmation (e.g., “Upload successful: 950 entries inserted.”).  

---

## 12. Non-Functional Requirements & Performance

1. **Lookup Performance**  
   - Aim for < 50 ms response time for single IP lookup (given an index on `ip_start/ip_end`).  
   - For batch CIDR lookups (e.g., /16 vs. /32), worst‐case ~200 ms assuming 200k records.  
2. **Scalability**  
   - With SQLite, reliably handle up to ~100 million rows if properly indexed, but eventually switch to PostgreSQL for > 10 million.  
   - Design code to allow swapping connection string; minimize raw SQL so migration is easy.  
3. **Concurrency**  
   - Use Uvicorn with at least 4 worker processes for production.  
   - Use an async DB driver (e.g., `asyncpg` if using PostgreSQL; for SQLite, use `Databases` library with `aiosqlite`).
4. **Uptime**  
   - Deploy with a process supervisor (e.g., Gunicorn + Uvicorn) behind an NGINX reverse proxy.  
   - Health‐check endpoint (`GET /health‐check/`) returns 200 in < 10 ms.  
   - If unhealthy (e.g., cannot connect to DB), return 503.  
5. **Logging & Error Tracking**  
   - Capture unhandled exceptions and send to an error‐tracking service (e.g., Sentry).  
   - Log at least: timestamp, request path, client IP, exception trace.  
6. **Backup / Disaster Recovery**  
   - Daily incremental backups of DB.  
   - Automated test restoring from backup once per week to ensure integrity.  

---

## 13. Example Workflow Scenarios

1. **Anonymous User IP Lookup**  
   - User navigates to `/` → Landing page loads (HTML + Tailwind CSS).  
   - Enters “*******” and clicks “Lookup.”  
   - Front-end JS sends `POST /api/v1/lookup/ { "query": "*******" }`.  
   - Back‐end:  
     - Validates “*******” as IPv4.  
     - Converts to `target = 134744072` (integer).  
     - Runs query: `SELECT * FROM ip_records WHERE ip_start <= 134744072 AND ip_end >= 134744072;`  
     - Finds record: `{ ip_cidr:"*******/24", country:"United States", owner:"Google LLC", … }`.  
   - Back‐end returns `200 OK` with JSON record.  
   - Front-end renders a dark card with the metadata.  

2. **Admin Bulk Upload Failure**  
   - Admin logs in at `/login/`, obtains JWT, stored in `localStorage`.  
   - Navigates to `/admin/upload/`.  
   - Chooses a text file with 1,000 lines; 60 have invalid IPs, 10 overlap existing ranges.  
   - Upload JS calls `POST /api/v1/admin/upload/` with `multipart/form-data`.  
   - Back‐end:  
     - Reads file line by line (streaming).  
     - First counts total lines = 1,000.  
     - Validates each; invalid_count = 60; conflict_count = 10. → total_bad = 70 = 7% > 5% threshold.  
     - Rolls back entire transaction.  
     - Returns `400 Bad Request` with:  
       ```json5
       {
         "detail": "More than 5% of lines failed validation. Bulk upload aborted.",
         "errors": [
           { "line": 5, "reason": "Invalid CIDR format" },
           { "line": 42, "reason": "Overlaps existing range ***********/16" },
           // ...
         ]
       }
       ```  
   - Front-end displays a red toast: “Bulk upload failed. See errors below.” and lists errors.  

3. **Admin Filter & Export**  
   - Admin navigates to `/admin/query/`, sets filter “Country = India,” “Owner contains ‘Airtel’,” hits “Search.”  
   - Front-end sends `POST /api/v1/admin/query/` with JSON filters.  
   - Back‐end returns `total_matches: 45`, data array of 25 (default pagination = 25), plus pagination metadata.  
   - Front-end displays table; admin scrolls through pages.  
   - Admin then clicks “Export All to CSV”: calls `GET /api/v1/admin/export/`.  
   - Back‐end streams back a 45-row CSV; front-end triggers download.  

---

## 14. Deployment Checklist

1. **Pre‐Production**  
   - [ ] Environment variables configured: `API_ENV=production`, `DATABASE_URL`, `JWT_SECRET`, `ADMIN_USERS` seeded.  
   - [ ] Tailwind CSS built in production mode (`NODE_ENV=production`, `purge` unused classes).  
   - [ ] Static files (CSS/JS) deployed to CDN or served via NGINX.  
   - [ ] Docker image built with tags: `latest`, `v1.0.0`.  
   - [ ] CI pipeline verifies linting (`flake8`/`black`), type checking (`mypy`), and tests (`pytest`).  
   - [ ] Sentry (or equivalent) DSN configured.  
   - [ ] Rate‐limit configuration tested.  
2. **Production**  
   - [ ] Deployed to Kubernetes cluster (3 replicas), behind ingress controller (NGINX).  
   - [ ] Health checks configured for Kubernetes liveness/readiness probes (`/health-check/`).  
   - [ ] Horizontal Pod Autoscaler (HPA) configured to scale pods when CPU > 70%.  
   - [ ] Database backups scheduled.  
   - [ ] CORS locked to `https://yourproductiondomain.com`.  
   - [ ] TLS certificates obtained via Let’s Encrypt (Cert-Manager).  
   - [ ] WAF (Web Application Firewall) rules in front of ingress (block SQL injection, rate limit).  
   - [ ] Monitoring dashboards set up in Grafana/Prometheus (API latency, error rates, DB connection count).  
   - [ ] Logging aggregator (ELK/CloudWatch) ingestion configured.  
3. **Post-Launch**  
   - [ ] First 24 hours: Monitor error rate (< 1%), CPU/memory usage.  
   - [ ] Solicit feedback from users/admins to refine filtering UI.  
   - [ ] Schedule monthly dependency updates (e.g., `pip‐compile` to upgrade Python packages).  
   - [ ] Rotate JWT secret and admin passwords every 6 months.  

---

## 15. Summary of AI-Style “Rule Content”

Below is a concise, bullet-point summary of the “AI rule” logic and business rules that govern the web application’s behavior:

1. **IP Parsing & Storage**  
   - Accept IPv4, IPv6, CIDR notation.  
   - Always store numeric ranges (`ip_start`, `ip_end`) for efficient range queries.  
   - Reject invalid IPs/CIDRs with 400 error.  
   - Disallow overlapping ranges unless explicitly intended; return conflicts.  

2. **Lookup Endpoint**  
   - Single IP → return at most one matching range (longest/more specific).  
   - CIDR → return all overlapping database entries.  
   - Return consistent JSON schema with `ip_cidr`, `ip_version`, `country`, `owner`, `added_at`, `comment`.  
   - If no result, 404 with “No data found for [query].”

3. **Admin Authentication**  
   - OAuth2 Password flow (JWT).  
   - Lockout after 5 failed attempts in 10 minutes; unlock after 30 minutes.  
   - Password complexity enforced.  
   - Store only bcrypt‐hashed passwords.

4. **Admin Operations**  
   - **Export**: Always returns the full CSV in streaming mode (no memory blow-up).  
   - **Upload**:  
     - Single: Validate, check overlap, insert or skip if duplicate.  
     - Bulk: Stream parse, must have ≤ 5% invalid/overlap.  
       - Otherwise, rollback entire batch.  
     - Maintain transaction integrity; report per‐line errors.  
   - **Filtered Search**:  
     - Dynamic filters by column + operator; parameterized to avoid SQL injection.  
     - Pagination + sorting.  
     - Return `total_matches` for front-end to build pagination UI.  

5. **UI/UX**  
   - Minimalist, dark-mode first.  
   - Consistent color palette.  
   - Responsive for mobile.  
   - Form validation both client + server.  
   - Toast notifications for success/failure messages.  

6. **Security**  
   - Enforce HTTPS.  
   - CORS restricted.  
   - Rate limit public endpoint (30 req/min per IP).  
   - JWT secrets managed in env vars; rotate regularly.  
   - Logging + monitoring must capture errors, failed auth, bulk failures.  
   - Audit all admin actions in `audit_logs` table.  

7. **Performance & Scalability**  
   - Use proper SQL indexes (`ip_start/ip_end`, `country`, `owner`, `added_at`).  
   - Benchmarks: < 50 ms lookup for single IP; < 200 ms for larger range queries.  
   - Easily swap from SQLite → PostgreSQL by changing connection string.  


By adhering to the above rules—validation logic, database schema, API contracts, UI/UX guidelines, and deployment best practices—you will ensure the IP lookup web application is secure, performant, maintainable, and provides a seamless user experience consistent with established services like IPWhois.io.

*End of AI Rule Content for IP Lookup Web Application*
