{% extends "base.html" %}

{% block title %}IP Lookup - Home{% endblock %}

{% block content %}
<div class="min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-dark-card shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-dark-accent">IP-Check</h1>
                </div>
                <nav>
                    <a href="/login" class="text-dark-accent hover:text-dark-secondary transition-colors duration-200 font-medium">
                        Admin <PERSON>gin
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <main class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-dark-text mb-2">
                    IP Address Lookup
                </h2>
                <p class="text-dark-text/70 text-lg">
                    Enter IPv4, IPv6, or Subnet
                </p>
            </div>
            
            <!-- Search Form -->
            <form id="lookup-form" class="space-y-6">
                <div>
                    <input 
                        id="ip-input" 
                        type="text" 
                        placeholder="e.g., ******* or ***********/24"
                        class="w-full px-4 py-3 bg-dark-input border border-gray-600 rounded-lg text-dark-text placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-dark-accent focus:border-transparent transition-all duration-200"
                        required
                    >
                </div>
                
                <button 
                    type="submit" 
                    id="lookup-btn"
                    class="w-full bg-dark-accent hover:bg-dark-secondary text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                    <span class="lookup-text">Lookup</span>
                    <div class="spinner ml-2" style="display: none;"></div>
                </button>
            </form>
            
            <p class="text-center text-dark-text/50 text-sm">
                Powered by local IP database
            </p>
            
            <!-- Results Section -->
            <div id="results-section" class="hidden">
                <div id="results-container" class="bg-dark-card border border-gray-600 rounded-lg p-6 space-y-4">
                    <!-- Results will be populated here -->
                </div>
            </div>
            
            <!-- No Results Message -->
            <div id="no-results" class="hidden">
                <div class="bg-dark-card border border-gray-600 rounded-lg p-6 text-center">
                    <p class="text-dark-text/70">No data found for the specified IP/CIDR</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark-card mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex justify-center space-x-6 text-sm text-dark-text/50">
                <a href="#" class="hover:text-dark-accent transition-colors duration-200">Privacy Policy</a>
                <a href="#" class="hover:text-dark-accent transition-colors duration-200">Contact</a>
                <a href="#" class="hover:text-dark-accent transition-colors duration-200">Docs</a>
            </div>
        </div>
    </footer>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('lookup-form');
    const input = document.getElementById('ip-input');
    const button = document.getElementById('lookup-btn');
    const resultsSection = document.getElementById('results-section');
    const resultsContainer = document.getElementById('results-container');
    const noResults = document.getElementById('no-results');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const query = input.value.trim();
        if (!query) {
            showToast('Please enter an IP address or CIDR', 'error');
            return;
        }
        
        // Show loading state
        setLoading(button, true);
        hideResults();
        
        try {
            const data = await apiCall('/api/v1/lookup/', {
                method: 'POST',
                body: JSON.stringify({ query: query }),
                skipAuth: true
            });
            
            displayResults(data);
            
        } catch (error) {
            if (error.message.includes('No data found')) {
                showNoResults();
            } else {
                showToast(error.message || 'Lookup failed', 'error');
            }
        } finally {
            setLoading(button, false);
        }
    });
    
    function displayResults(results) {
        if (!results || results.length === 0) {
            showNoResults();
            return;
        }
        
        resultsContainer.innerHTML = '';
        
        results.forEach((result, index) => {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'border-l-4 border-dark-accent pl-4';
            
            resultDiv.innerHTML = `
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <span class="text-dark-accent font-semibold">CIDR:</span>
                        <span class="text-dark-text">${escapeHtml(result.ip_cidr)}</span>
                        <span class="text-xs bg-dark-input px-2 py-1 rounded text-dark-text/70">IPv${result.ip_version}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-dark-accent font-semibold">Country:</span>
                        <span class="text-dark-text">${escapeHtml(result.country)}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-dark-accent font-semibold">Owner:</span>
                        <span class="text-dark-text">${escapeHtml(result.owner)}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-dark-accent font-semibold">Added:</span>
                        <span class="text-dark-text/70 text-sm">${formatDate(result.added_at)}</span>
                    </div>
                    ${result.comment ? `
                        <div class="flex items-start space-x-2">
                            <span class="text-dark-accent font-semibold">Comment:</span>
                            <span class="text-dark-text/70 text-sm">${escapeHtml(result.comment)}</span>
                        </div>
                    ` : ''}
                </div>
            `;
            
            resultsContainer.appendChild(resultDiv);
            
            // Add separator between results
            if (index < results.length - 1) {
                const separator = document.createElement('hr');
                separator.className = 'border-gray-600';
                resultsContainer.appendChild(separator);
            }
        });
        
        resultsSection.classList.remove('hidden');
        noResults.classList.add('hidden');
    }
    
    function showNoResults() {
        resultsSection.classList.add('hidden');
        noResults.classList.remove('hidden');
    }
    
    function hideResults() {
        resultsSection.classList.add('hidden');
        noResults.classList.add('hidden');
    }
});
</script>
{% endblock %}
