import logging
from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, HTTPException, status, Request
from models import LoginRequest, LoginResponse, RegisterRequest, RegisterResponse
from auth import authenticate_user, create_access_token, create_refresh_token, ACCESS_TOKEN_EXPIRE_MINUTES, create_admin_user, validate_password_strength

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login/", response_model=LoginResponse)
async def login(request: Request, login_data: LoginRequest):
    """
    Authenticate admin user and return JWT tokens.
    
    Validates credentials and returns access token for admin operations.
    """
    try:
        # Get client IP for logging
        client_ip = request.client.host
        
        # Authenticate user
        user = await authenticate_user(
            username=login_data.username,
            password=login_data.password,
            client_ip=client_ip
        )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["username"], "user_id": user["id"]},
            expires_delta=access_token_expires
        )
        
        # Create refresh token (optional)
        refresh_token = create_refresh_token(
            data={"sub": user["username"], "user_id": user["id"]}
        )
        
        logger.info(f"Successful login for user: {user['username']} from IP: {client_ip}")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert to seconds
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error for {login_data.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during authentication"
        )


@router.post("/register/", response_model=RegisterResponse)
async def register(request: Request, register_data: RegisterRequest):
    """
    Register a new admin user.

    Creates a new admin account with username and password validation.
    """
    try:
        # Get client IP for logging
        client_ip = request.client.host

        # Validate password confirmation
        if register_data.password != register_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Passwords do not match"
            )

        # Validate password strength
        if not validate_password_strength(register_data.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password must be at least 8 characters and include uppercase, lowercase, digit, and symbol"
            )

        # Create the admin user
        user_id = await create_admin_user(register_data.username, register_data.password)

        logger.info(f"New admin user registered: {register_data.username} from IP: {client_ip}")

        return RegisterResponse(
            message="Account created successfully! You can now log in.",
            username=register_data.username
        )

    except ValueError as e:
        # Handle username already exists or other validation errors
        if "already exists" in str(e):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Username already exists"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error for {register_data.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during registration"
        )


@router.post("/refresh/")
async def refresh_token(refresh_token: str):
    """
    Refresh access token using refresh token.

    Optional endpoint for token refresh functionality.
    """
    # This is a placeholder for refresh token functionality
    # Implementation would verify refresh token and issue new access token
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Refresh token functionality not implemented"
    )
