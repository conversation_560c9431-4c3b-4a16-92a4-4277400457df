import logging
from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, HTTPException, status, Request
from models import LoginRequest, LoginResponse
from auth import authenticate_user, create_access_token, create_refresh_token, ACCESS_TOKEN_EXPIRE_MINUTES

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login/", response_model=LoginResponse)
async def login(request: Request, login_data: LoginRequest):
    """
    Authenticate admin user and return JWT tokens.
    
    Validates credentials and returns access token for admin operations.
    """
    try:
        # Get client IP for logging
        client_ip = request.client.host
        
        # Authenticate user
        user = await authenticate_user(
            username=login_data.username,
            password=login_data.password,
            client_ip=client_ip
        )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["username"], "user_id": user["id"]},
            expires_delta=access_token_expires
        )
        
        # Create refresh token (optional)
        refresh_token = create_refresh_token(
            data={"sub": user["username"], "user_id": user["id"]}
        )
        
        logger.info(f"Successful login for user: {user['username']} from IP: {client_ip}")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert to seconds
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error for {login_data.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during authentication"
        )


@router.post("/refresh/")
async def refresh_token(refresh_token: str):
    """
    Refresh access token using refresh token.
    
    Optional endpoint for token refresh functionality.
    """
    # This is a placeholder for refresh token functionality
    # Implementation would verify refresh token and issue new access token
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Refresh token functionality not implemented"
    )
